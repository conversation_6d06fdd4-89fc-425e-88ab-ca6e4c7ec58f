from fastapi import APIRouter, Depends, Request, Form, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
from config.enums import BusinessType
from config.get_db import get_db
from module_admin.annotation.log_annotation import Log
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.service.auth_service import AuthService
from module_admin.service.auth_adapter import AuthAdapter
from module_admin.service.internal_user_login_service import oauth2_scheme
from utils.log_util import logger
from utils.response_util import ResponseUtil
from utils.exception_util import ExceptionUtil
from utils.message_util import send_sms_verification_code
from exceptions.exception import LoginException, AuthException, BusinessException

# 使用统一的API路由前缀格式
# 注意：登录接口不需要身份验证
auth_controller = APIRouter(prefix='/api/v1/auth')

@auth_controller.post('/login', response_model=CrudResponseModel, summary="用户登录")
@Log(title='用户登录', business_type=BusinessType.OTHER, log_type='login')
async def login(
    request: Request,
    mobile: str = Form(..., description="用户手机号"),
    password: str = Form(..., description="用户密码"),
    query_db: AsyncSession = Depends(get_db)
):
    """用户登录接口

    通过手机号和密码进行用户登录，返回用户信息和令牌

    原始API路径: /auth/login
    """
    try:
        # 获取系统时间戳参数（如果有）
        xmjz_time = request.query_params.get("xmjz_time")

        # 调用服务层进行用户登录
        result = await AuthService.login_service(request, query_db, mobile, password)

        return ResponseUtil.success(
            msg="登录成功",
            data=result
        )
    except LoginException as e:
        logger.error(f"登录失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except AuthException as e:
        logger.error(f"认证失败: {e.message}")
        return ResponseUtil.unauthorized(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"系统异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@auth_controller.post('/login-by-code', response_model=CrudResponseModel, summary="验证码登录")
@Log(title='验证码登录', business_type=BusinessType.OTHER, log_type='login')
async def login_by_code(
    request: Request,
    mobile: str = Form(..., description="用户手机号"),
    code: str = Form(..., description="短信验证码"),
    query_db: AsyncSession = Depends(get_db)
):
    """验证码登录接口

    通过手机号和短信验证码进行用户登录，返回用户信息和令牌

    原始API路径: /auth/login-by-code
    """
    try:
        # 调用服务层进行验证码登录
        result = await AuthService.login_by_code_service(request, query_db, mobile, code)

        return ResponseUtil.success(
            msg="登录成功",
            data=result
        )
    except LoginException as e:
        logger.error(f"验证码登录失败: {e.message}")
        return ResponseUtil.failure(msg=e.message, code=e.code, data=e.data)
    except AuthException as e:
        logger.error(f"验证码认证失败: {e.message}")
        return ResponseUtil.unauthorized(msg=e.message, code=e.code, data=e.data)
    except BusinessException as e:
        logger.error(f"验证码登录业务处理失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"验证码登录系统异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@auth_controller.get('/setting', summary="获取认证设置", dependencies=[Depends(AuthAdapter.get_current_user)])
async def get_setting(
    request: Request,
    query_db: AsyncSession = Depends(get_db)
):
    """获取认证设置接口

    获取系统认证相关的配置信息

    原始API路径: /auth/setting
    """
    try:
        # 获取系统时间戳参数（如果有）
        xmjz_time = request.query_params.get("xmjz_time")

        # 调用服务层获取认证设置
        result = await AuthService.get_setting_service(query_db)

        return ResponseUtil.success(
            msg="获取认证设置成功",
            data=result
        )
    except BusinessException as e:
        logger.error(f"获取认证设置失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取认证设置异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@auth_controller.get('/bootLog', summary="获取启动日志", dependencies=[Depends(AuthAdapter.get_current_user)])
async def get_boot_log(
    request: Request,
    query_db: AsyncSession = Depends(get_db)
):
    """获取启动日志接口

    获取系统启动相关的日志信息

    原始API路径: /auth/bootLog
    """
    try:
        # 获取系统时间戳参数（如果有）
        xmjz_time = request.query_params.get("xmjz_time")

        # 调用服务层获取启动日志
        result = await AuthService.get_boot_log_service(query_db)

        return ResponseUtil.success(
            msg="获取启动日志成功",
            data=result
        )
    except BusinessException as e:
        logger.error(f"获取启动日志失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"获取启动日志异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])

@auth_controller.post('/logout', summary="用户退出登录")
@Log(title='用户退出登录', business_type=BusinessType.OTHER, log_type='logout')
async def logout(
    request: Request,
    token: Optional[str] = Depends(oauth2_scheme)
):
    """用户退出登录接口

    清除用户token和session信息，退出登录

    原始API路径: /auth/logout
    """
    try:
        # 调用服务层进行用户退出登录
        result = await AuthService.logout_service(request, token)

        return ResponseUtil.success(
            msg="退出登录成功",
            data=result
        )
    except BusinessException as e:
        logger.error(f"用户退出登录失败: {e.message}")
        return ResponseUtil.business_error(msg=e.message, code=e.code, data=e.data)
    except Exception as e:
        # 使用异常分析工具类分析未知异常
        error_info = ExceptionUtil.handle_exception(e)
        logger.error(f"用户退出登录异常: {error_info['message']}")
        return ResponseUtil.error(msg=error_info['message'], code=error_info['code'], data=error_info['data'])
