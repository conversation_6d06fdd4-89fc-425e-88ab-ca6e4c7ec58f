{"permissions": {"allow": ["Bash(find:*)", "Bash(grep:*)", "<PERSON><PERSON>(python:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(sed:*)", "Bash(rm /Users/<USER>/Desktop/小程序/storeui/src/utils/local-qrcode.js)", "Bash(rm /Users/<USER>/Desktop/小程序/storeui/src/utils/perfect-qrcode.js)", "Bash(rm /Users/<USER>/Desktop/小程序/storeui/src/utils/miniprogram-qrcode.js)", "Bash(rm /Users/<USER>/Desktop/小程序/backend_qrcode_api.py)", "Bash(find /Users/<USER>/Desktop/小程序/storeui/src/utils -name \"*qr*\" -type f)", "Bash(find /Users/<USER>/Desktop/小程序 -name \"*.env*\" -o -name \"*.config.js\" -o -name \"vue.config.js\")", "Bash(find /Users/<USER>/Desktop/小程序/storeapi -name \"*.py\" -path \"*/entity/do/*\")"], "deny": []}}